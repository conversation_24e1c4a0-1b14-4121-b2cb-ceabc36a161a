package ctrl

import (
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/abiosoft/ishell/v2"
	"github.com/go-resty/resty/v2"

	"github.com/gaoxing520/pms-cli/pkg/consts"
	"github.com/gaoxing520/pms-cli/pkg/log"
	"github.com/gaoxing520/pms-cli/pkg/utils"
)

func AddUserCmd(shell *ishell.Shell, cli *Ctrl) {
	shell.AddCmd(&ishell.Cmd{
		Name: "login",
		Help: "login [<username> <password> | <username> <password> <host> <port>]",
		Func: func(c *ishell.Context) {
			cli.runShellCmd(c, cli.login)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name: "logout",
		Help: "logout",
		Func: func(c *ishell.Context) {
			cli.runShellCmd(c, cli.logout)
		},
	})

	shell.AddCmd(&ishell.Cmd{
		Name: "show",
		Help: "show",
		Func: func(c *ishell.Context) {
			cli.runShellCmd(c, cli.show)
		},
	})
}

func (cli *Ctrl) login(c *ishell.Context) (any, error) {
	cli.lock.Lock()
	defer cli.lock.Unlock()

	var (
		host     = cli.config.Host
		port     = cli.config.Port
		username = cli.config.Username
		password = cli.config.Password
		resp     *resty.Response
		err      error
	)

	switch len(c.Args) {
	case 0:
	case 2:
		username = strings.TrimSpace(c.Args[0])
		password = strings.TrimSpace(c.Args[1])
	case 4:
		username = strings.TrimSpace(c.Args[0])
		password = strings.TrimSpace(c.Args[1])
		host = strings.TrimSpace(c.Args[2])
		port, err = strconv.Atoi(strings.TrimSpace(c.Args[3]))
		if err != nil {
			log.Panic().Err(err).Send()
		}

	default:
		return nil, fmt.Errorf("user-login [<username> <password> | <username> <password> <host> <port>]")
	}

	resp, err = cli.client.
		R().
		SetResult(&LoginResp{}).
		SetBody(
			utils.JsonMarshal(
				&LoginPayload{
					Username: username,
					Password: password,
				},
			),
		).
		Post(fmt.Sprintf("http://%s:%d%s", host, port, consts.UrlApiLogin))

	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("response code=%d, msg=%s", resp.StatusCode(), resp.String())
	}

	result := resp.Result().(*LoginResp)
	if result.Code != "" && result.Msg != "success" {
		return nil, fmt.Errorf("result error code=%s, description=%s", result.Code, result.Msg)
	}

	if setCookie := resp.Header().Get("Set-Cookie"); setCookie != "" {
		for _, s := range strings.Split(setCookie, ";") {
			if strings.HasPrefix(s, "JSESSIONID") {
				cli.JSESSIONID = strings.Split(s, "=")[1]

				cli.client.Cookies = []*http.Cookie{
					{
						Name:  "JSESSIONID",
						Value: cli.JSESSIONID,
					},
				}

				log.Debug().Str("Cookie", "JSESSIONID").Msgf("Value: %s", utils.PrettyJsonMarshal(cli.client.Cookies))

				break
			}
		}
	}

	cli.client.SetAuthToken(result.Data.Token)

	cli.config.Host = host
	cli.config.Port = port
	cli.config.Username = username
	cli.config.Port = port

	if err = os.WriteFile(filepath.Join(consts.DefaultAppConfigPath, fmt.Sprintf("%s.%s", consts.DefaultAppConfigName, consts.DefaultAppConfigType)), []byte(utils.PrettyJsonMarshal(cli.config)), 0644); err != nil {
		log.Panic().Err(err).Send()
	}

	return nil, nil
}

func (cli *Ctrl) logout(_ *ishell.Context) (any, error) {
	cli.lock.Lock()
	defer cli.lock.Unlock()

	var (
		resp *resty.Response
		err  error
	)

	req := cli.client.R()
	req.Header.Del("Cookie")

	resp, err = req.Post(
		fmt.Sprintf(
			"http://%s:%d%s",
			cli.config.Host,
			cli.config.Port,
			consts.UrlApiLogout,
		),
	)

	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		return nil, fmt.Errorf("response code=%d, msg=%s", resp.StatusCode(), resp.String())
	}

	cli.client.Cookies = nil
	cli.client.Token = ""
	cli.JSESSIONID = ""

	return nil, nil
}

type LoginResp struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Token string `json:"token"`
	} `json:"data"`
}

type LoginPayload struct {
	Username string `json:"username"`
	Password string `json:"password"`
}
