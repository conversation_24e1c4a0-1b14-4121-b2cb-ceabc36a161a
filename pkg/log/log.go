package log

import (
	"io"
	"os"

	"github.com/rs/zerolog"
)

var DefaultLogger = zerolog.New(os.Stderr).With().Caller().Logger()

func Output(w io.Writer) zerolog.Logger {
	return DefaultLogger.Output(w)
}

func Trace() *zerolog.Event {
	return DefaultLogger.Trace()
}

func Debug() *zerolog.Event {
	return DefaultLogger.Debug()
}

func Info() *zerolog.Event {
	return DefaultLogger.Info()
}

func Warn() *zerolog.Event {
	return DefaultLogger.Warn()
}

func Error() *zerolog.Event {
	return DefaultLogger.Error()
}

func Fatal() *zerolog.Event {
	return DefaultLogger.Fatal()
}

func Panic() *zerolog.Event {
	return DefaultLogger.Panic()
}

func JustPanic(err error) {
	if err != nil {
		Panic().Err(err).Msgf("%+v", err)
	}
}
